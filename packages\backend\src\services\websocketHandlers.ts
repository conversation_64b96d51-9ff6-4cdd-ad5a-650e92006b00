import {
  WebSocketClient,
  ChatMessageRequest,
  sendMessage,
  sendError,
} from "./websocketUtils";
import {
  getOrCreateSession,
  updateSessionData,
  addMessageToHistory,
} from "./session";
import { generateChatResponseStream } from "./claude";
import { ensureSystemInstruction } from "@config/ai";
import { ChatMessage, InputMessage, BackendChatMessage } from "@types";
import { logger } from "@utils/logger";

// --- Message Handlers ---

/**
 * Handles incoming 'chat_message' requests.
 */
export async function handleChatMessage(
  client: WebSocketClient,
  sessionId: string,
  request: ChatMessageRequest
) {
  const userMessageContent = request.payload.message;
  // INFO log for starting processing
  logger.info(
    { sessionId, messageLength: userMessageContent.length },
    "[WebSocket] Processing user message"
  );

  // Retrieve session data - still needed for history and previousResponseId
  const { sessionData } = getOrCreateSession(sessionId);
  const previousResponseId = sessionData.previousResponseId;

  // Create the user message object
  const newUserMessage: ChatMessage = {
    role: "user",
    content: userMessageContent,
  };

  // Add user message to conceptual history
  addMessageToHistory(sessionId, newUserMessage); // TRACE log in session.ts

  // Prepare input for Claude
  let messagesForAPI: Array<InputMessage>;
  if (!previousResponseId) {
    // Starting a new chain: send history including the new user message, ensure system prompt
    messagesForAPI = ensureSystemInstruction(sessionData.chatHistory);
  } else {
    // Continuing a chain: only send the new user message
    messagesForAPI = [newUserMessage];
  }

  // Send stream start notification - DEBUG log via sendMessage
  sendMessage(client, { type: "stream_start", payload: { sessionId } });

  // INFO log for initiating AI call
  logger.info(
    { sessionId, isNewChain: !previousResponseId },
    "[WebSocket] Initiating AI stream"
  );

  try {
    // Generate chat response without function calling
    await generateChatResponseStream(
      messagesForAPI,
      // onChunk: Handle text delta - TRACE log via sendMessage
      (chunk) => {
        sendMessage(client, { type: "stream_chunk", payload: { text: chunk } });
      },
      // onComplete: Handle stream completion
      (finalText: string | null, responseId: string) => {
        // INFO log for completion (Claude log has details)
        logger.info(
          {
            sessionId,
            responseId,
            hasText: !!finalText, // Indicate if text was generated
          },
          "[WebSocket] AI response processing complete"
        );

        // Create the final assistant message
        if (finalText && finalText.trim().length > 0) {
          const finalAssistantMessage: BackendChatMessage = {
            role: "assistant",
            content: finalText,
          };
          updateSessionData(sessionId, responseId, finalAssistantMessage); // TRACE log
        } else {
          // Fallback if no text generated
          logger.warn(
            { sessionId, responseId },
            "[WebSocket] No text generated by AI."
          );
          const minimalAssistantMessage: BackendChatMessage = {
            role: "assistant",
            content: "",
          };
          updateSessionData(sessionId, responseId, minimalAssistantMessage); // TRACE log
        }

        // Send stream end notification - DEBUG log via sendMessage
        sendMessage(client, {
          type: "stream_end",
          payload: { responseId, sessionId },
        });
      },
      // onError: Handle errors during the stream
      (error: Error) => {
        // ERROR: Claude stream error is critical
        logger.error(error, "[WebSocket] Claude stream error during chat", {
          sessionId,
        });
        sendMessage(client, {
          type: "stream_error",
          payload: { message: error.message },
        }); // DEBUG log
        sendError(client, `AI stream failed: ${error.message}`, "AI_ERROR"); // WARN log
      }
    );
  } catch (err) {
    // ERROR: Failure to even start the stream is critical
    const error = err instanceof Error ? err : new Error(String(err));
    logger.error(error, "[WebSocket] Failed to setup Claude stream for chat", {
      sessionId,
    });
    sendError(client, "Failed to initiate AI stream.", "INTERNAL_ERROR"); // WARN log
  }
}
