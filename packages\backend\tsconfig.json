{
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "lib": ["ES2020"],
    "outDir": "./dist",
    // Configure rootDir to point to src directory
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    // baseUrl should be '.' (the directory of tsconfig.json) when using paths
    "baseUrl": ".",
    "paths": {
      // Define paths relative to baseUrl
      "@common/*": ["../common/src/*"],
      "@config/*": ["./src/config/*"],
      "@controllers/*": ["./src/controllers/*"],
      "@services/*": ["./src/services/*"],
      "@utils/*": ["./src/utils/*"],
      "@routes/*": ["./src/routes/*"],
      "@middleware/*": ["./src/middleware/*"],
      "@tools/*": ["./src/tools/*"],
      "@types": ["./src/types"],
      "@constants/*": ["./src/constants/*"]
    },
    "sourceMap": true,
    "moduleResolution": "node",
    "composite": true
  },
  // Include only source files within this package's src directory
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"],
  "references": [{ "path": "../common" }]
}
